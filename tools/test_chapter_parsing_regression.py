#!/usr/bin/env python3
"""
Регрессионный тест для логики разбиения на главы.

Проверяет, что изменения в логике парсинга не ломают уже работающие случаи.
Использует реальные архивы и проверенные вручную результаты.

Запуск:
    python test_chapter_parsing_regression.py
    python test_chapter_parsing_regression.py --verbose
    python test_chapter_parsing_regression.py --only-failed
"""

import argparse
import sys
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Optional

# Добавляем корневую директорию проекта в путь
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.processing.parsers.fb2.chapter_aggregator import (  # noqa: E402
    ChapterAggregator,
)
from app.processing.parsers.fb2.fb2_parser import FB2Parser  # noqa: E402
from app.processing.parsers.fb2.markdown_renderer import MarkdownRenderer  # noqa: E402
from app.storage import LocalStorageManager  # noqa: E402


@dataclass
class TestCase:
    """Тестовый случай для проверки разбиения на главы."""

    name: str  # Человекочитаемое имя теста
    file_path: str  # Путь к файлу (может быть archive.zip::book.fb2)
    expected_chapters: int  # Ожидаемое количество глав (проверено вручную)
    description: str = ""  # Описание особенностей разметки
    strategy: Optional[str] = None  # Ожидаемая стратегия (опционально)


# 📋 РЕЕСТР ТЕСТОВЫХ СЛУЧАЕВ
# Добавляйте сюда новые случаи по мере обнаружения проблемных книг
TEST_CASES = [
    # 🔽 ДОБАВЛЯЙТЕ НОВЫЕ СЛУЧАИ ЗДЕСЬ:
    # 🆕 НОВЫЙ ПАТТЕРН [N] - добавлен 2025-07-18
    # Поддержка числовых маркеров глав в квадратных скобках
    # Структура: <body><section><p>[1]</p>...<p>[16]</p></section>
    # До исправления: 1 глава "Пролог" → аномалия few_chapters
    # После исправления: 16 глав ([1]-[16]) → STRUCTURAL стратегия
    TestCase(
        name="Паттерн [N] в квадратных скобках",
        file_path="/mnt/d/Project/books/zip/zip_flibusta/811194-815075.zip::811232.fb2",
        expected_chapters=16,
        description="Числовые маркеры глав в квадратных скобках: <p>[1]</p>, <p>[2]</p>, ..., <p>[16]</p>",
        strategy="STRUCTURAL",
    ),
    TestCase(
        name="Сложная комюбинированная иерархическая структура",
        file_path="/mnt/d/Project/books/zip/zip_flibusta/811194-815075.zip::811278.fb2",
        expected_chapters=96,
        description="Комбинация секций <section><title><p>1</p></title> и параграфов с внутренними числовыми заголовками <strong>1</strong>, <strong>2</strong>...",
        strategy="STRUCTURAL",
    ),
    TestCase(
        name="Вложенная структурная разметка",
        file_path="/mnt/d/Project/books/zip/zip_flibusta/177718-183065.zip::177724.fb2",
        expected_chapters=8,
        description="Вложенные Секции section->section->title",
        strategy="STRUCTURAL",
    ),
    TestCase(
        name="Простая структурная разметка",
        file_path="/mnt/d/Project/books/zip/zip_flibusta/173909-177717.zip::173910.fb2",
        expected_chapters=18,
        description="Простые Секции с заголовками section->title",
        strategy="STRUCTURAL",
    ),
    # 📝 ПРИМЕРЫ РАЗНЫХ ТИПОВ СЛУЧАЕВ:
    # TestCase(
    #     name="Строгие паттерны заголовков",
    #     file_path="/path/to/archive.zip::book.fb2",
    #     expected_chapters=15,
    #     description="Заголовки типа 'Глава 1', 'Пролог', 'Эпилог'",
    #     strategy="STRICT"
    # ),
    # TestCase(
    #     name="Эвристические заголовки",
    #     file_path="/path/to/archive.zip::book.fb2",
    #     expected_chapters=8,
    #     description="Нестандартные заголовки, требующие эвристик",
    #     strategy="HEURISTIC"
    # ),
    # TestCase(
    #     name="Глубокое разбиение",
    #     file_path="/path/to/archive.zip::book.fb2",
    #     expected_chapters=25,
    #     description="Большие главы, разбиваемые по текстовым маркерам",
    #     strategy="DEEP_SPLIT"
    # ),
    # TestCase(
    #     name="Fallback случай",
    #     file_path="/path/to/archive.zip::book.fb2",
    #     expected_chapters=3,
    #     description="Минимальная разметка, все стратегии дают мало глав",
    #     strategy="FALLBACK"
    # ),
]


class ChapterParsingTester:
    """Тестер для проверки логики разбиения на главы."""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.storage_manager = LocalStorageManager()
        self.parser = FB2Parser()
        self.renderer = MarkdownRenderer()

        # Используем те же настройки, что и в продакшене
        self.aggregator = ChapterAggregator(
            markdown_renderer=self.renderer,
            min_chapters_threshold=7,  # Из .env
        )

    def run_all_tests(self, only_failed: bool = False) -> bool:
        """Запускает все тесты и возвращает True если все прошли."""
        if not only_failed:
            print("🧪 Регрессионный тест логики разбиения на главы")
            print("=" * 60)

        if not TEST_CASES:
            print("⚠️ Нет тестовых случаев для проверки")
            return True

        results = []
        failed_count = 0
        start_time = time.time()

        for i, test_case in enumerate(TEST_CASES, 1):
            if self.verbose:
                print(f"\n🔍 Тест {i}/{len(TEST_CASES)}: {test_case.name}")
                print(f"   📁 Файл: {test_case.file_path}")
                print(f"   📝 Описание: {test_case.description}")

            try:
                result = self._run_single_test(test_case)
                results.append(result)

                if result["passed"]:
                    # Успешные тесты показываем только если НЕ режим only_failed
                    if not only_failed:
                        status = "✅"
                        if self.verbose:
                            print(f"   {status} Пройден: {result['actual_chapters']} глав")
                            print(f"   🎯 Стратегия: {result['actual_strategy']}")
                            if result["actual_strategy"] != test_case.strategy and test_case.strategy:
                                print(f"   ⚠️ Ожидалась стратегия: {test_case.strategy}")
                        else:
                            strategy_info = (
                                f" (стратегия: {result['actual_strategy']})" if result["actual_strategy"] else ""
                            )
                            print(f"{status} {test_case.name}{strategy_info}")
                else:
                    # Провалившиеся тесты показываем ВСЕГДА
                    failed_count += 1
                    status = "❌"
                    print(f"{status} {test_case.name}")
                    print(f"   Ожидалось: {test_case.expected_chapters} глав")
                    print(f"   Получено: {result['actual_chapters']} глав")
                    print(f"   Стратегия: {result['actual_strategy']}")
                    if result["actual_strategy"] != test_case.strategy and test_case.strategy:
                        print(f"   ⚠️ Ожидалась стратегия: {test_case.strategy}")

            except Exception as e:
                # Ошибки показываем ВСЕГДА
                failed_count += 1
                results.append({"passed": False, "error": str(e)})
                print(f"❌ {test_case.name}")
                print(f"   Ошибка: {e}")

        # Итоговая статистика
        total_time = time.time() - start_time
        passed_count = len(TEST_CASES) - failed_count

        # В режиме only_failed показываем статистику только если есть ошибки
        if only_failed and failed_count == 0:
            print("✅ Все тесты пройдены!")
            return True
        elif only_failed and failed_count > 0:
            print(f"\n❌ Провалено тестов: {failed_count} из {len(TEST_CASES)}")
            return False

        # Обычный режим - полная статистика
        print("\n" + "=" * 60)
        print("📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ")
        print("=" * 60)
        print(f"Всего тестов: {len(TEST_CASES)}")
        print(f"Пройдено: {passed_count}")
        print(f"Провалено: {failed_count}")
        print(f"Время выполнения: {total_time:.2f} сек")

        if failed_count == 0:
            print("\n🎉 Все тесты пройдены! Логика разбиения работает корректно.")
            return True
        else:
            print(f"\n⚠️ {failed_count} тестов провалено. Проверьте изменения в логике.")
            return False

    def _run_single_test(self, test_case: TestCase) -> dict:
        """Выполняет один тест и возвращает результат."""
        # Читаем файл
        if "::" in test_case.file_path:
            archive_path, book_filename = test_case.file_path.split("::", 1)
            file_stream = self.storage_manager.read_file_from_archive(archive_path, book_filename)
            file_content = file_stream.read()
        else:
            with open(test_case.file_path, "rb") as f:
                file_content = f.read()

        # Создаем временный файл для парсера
        import tempfile

        with tempfile.NamedTemporaryFile(mode="wb", suffix=".fb2", delete=False) as f:
            f.write(file_content)
            temp_file = f.name

        try:
            # Используем полный пайплайн как в продакшене
            from app.processing.parser_dispatcher import ParserDispatcher

            dispatcher = ParserDispatcher()
            canonical_book, parsing_report = dispatcher.parse_to_canonical(Path(temp_file))

            chapters = canonical_book.chapters
            used_strategy = parsing_report.diagnostics.get("chapter_heuristic", "unknown")

            # Проверяем результат
            actual_chapters = len(chapters)
            passed = actual_chapters == test_case.expected_chapters

            # Дополнительная проверка стратегии (если указана)
            if test_case.strategy and used_strategy != test_case.strategy:
                if self.verbose:
                    print(f"   ⚠️ Стратегия изменилась: {test_case.strategy} → {used_strategy}")

            return {
                "passed": passed,
                "actual_chapters": actual_chapters,
                "expected_chapters": test_case.expected_chapters,
                "actual_strategy": used_strategy,
                "expected_strategy": test_case.strategy,
            }

        finally:
            # Удаляем временный файл
            Path(temp_file).unlink()

    def add_test_case_interactive(self):
        """Интерактивное добавление нового тестового случая."""
        print("📝 Добавление нового тестового случая")
        print("-" * 40)

        name = input("Название теста: ")
        file_path = input("Путь к файлу (archive.zip::book.fb2): ")
        expected_chapters = int(input("Ожидаемое количество глав: "))
        description = input("Описание особенностей (опционально): ")
        strategy = input("Ожидаемая стратегия (опционально): ") or None

        # Формируем код для добавления
        test_code = f"""    TestCase(
        name="{name}",
        file_path="{file_path}",
        expected_chapters={expected_chapters},
        description="{description}","""

        if strategy:
            test_code += f'\n        strategy="{strategy}"'

        test_code += "\n    ),"

        print("\n📋 Добавьте этот код в TEST_CASES:")
        print("-" * 40)
        print(test_code)
        print("-" * 40)


def main():
    parser = argparse.ArgumentParser(description="Регрессионный тест логики разбиения на главы")
    parser.add_argument("--verbose", "-v", action="store_true", help="Подробный вывод")
    parser.add_argument(
        "--only-failed",
        action="store_true",
        help="Показывать только провалившиеся тесты",
    )
    parser.add_argument("--add", action="store_true", help="Интерактивное добавление нового теста")

    args = parser.parse_args()

    tester = ChapterParsingTester(verbose=args.verbose)

    if args.add:
        tester.add_test_case_interactive()
        return

    success = tester.run_all_tests(only_failed=args.only_failed)
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
